import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:camerawesome/camerawesome_plugin.dart';
import 'package:permission_handler/permission_handler.dart';
import 'dart:async';
import 'dart:io';
import 'dart:ui' as ui;
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:image/image.dart' as img;
import 'package:intl/intl.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:image_picker/image_picker.dart';
import 'gallery_page.dart';
import 'png_watermark_manager.dart';
import 'png_menu_page.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 设置系统UI样式
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.light,
      systemNavigationBarColor: Colors.black,
      systemNavigationBarIconBrightness: Brightness.light,
    ),
  );

  // 设置首选方向
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: '透卡相机',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      home: const CameraScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}

class CameraScreen extends StatefulWidget {
  const CameraScreen({super.key});

  @override
  State<CameraScreen> createState() => _CameraScreenState();
}

class _CameraScreenState extends State<CameraScreen>
    with WidgetsBindingObserver, TickerProviderStateMixin {
  // 相机状态
  bool _isInitialized = false;
  bool _isTakingPicture = false;
  final ImagePicker _picker = ImagePicker();

  // 水印管理器
  final PngWatermarkManager _watermarkManager = PngWatermarkManager();

  // UI状态
  bool _showFocusRing = false;
  Offset _focusPoint = const Offset(0.5, 0.5);
  double _currentZoom = 1.0;
  double _minZoom = 1.0;
  double _maxZoom = 8.0;
  bool _isFlashOn = false;
  bool _isFrontCamera = false;

  // 动画控制器
  late AnimationController _focusAnimationController;
  late Animation<double> _focusAnimation;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initializeAnimations();
    _requestPermissions();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _focusAnimationController.dispose();
    super.dispose();
  }

  void _initializeAnimations() {
    _focusAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _focusAnimation = Tween<double>(begin: 1.0, end: 0.0).animate(
      CurvedAnimation(parent: _focusAnimationController, curve: Curves.easeOut),
    );
  }

  Future<void> _requestPermissions() async {
    final permissions = [
      Permission.camera,
      Permission.microphone,
      Permission.storage,
    ];

    Map<Permission, PermissionStatus> statuses = await permissions.request();

    bool allGranted = statuses.values.every((status) => status.isGranted);

    if (!allGranted) {
      _showPermissionDialog();
    } else {
      setState(() {
        _isInitialized = true;
      });
    }
  }

  void _showPermissionDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('权限请求'),
          content: const Text('透卡相机需要相机、麦克风和存储权限才能正常工作。请在设置中授予这些权限。'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _requestPermissions();
              },
              child: const Text('重试'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                SystemNavigator.pop();
              },
              child: const Text('退出'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      return const Scaffold(
        backgroundColor: Colors.black,
        body: Center(child: CircularProgressIndicator(color: Colors.white)),
      );
    }

    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // CameraAwesome 相机预览
          _buildCameraAwesome(),

          // 聚焦环
          if (_showFocusRing) _buildFocusRing(),

          // 顶部状态栏
          _buildTopStatusBar(),

          // 底部控制栏
          _buildBottomControls(),

          // 右侧菜单按钮
          _buildRightMenuButton(),

          // 左侧镜头切换按钮
          _buildLeftLensButton(),
        ],
      ),
    );
  }

  Widget _buildCameraAwesome() {
    return CameraAwesomeBuilder.custom(
      saveConfig: SaveConfig.photo(),
      sensorConfig: SensorConfig.single(
        sensor: _isFrontCamera
            ? Sensor.position(SensorPosition.front)
            : Sensor.position(SensorPosition.back),
        flashMode: _isFlashOn ? FlashMode.on : FlashMode.none,
        zoom: _currentZoom,
      ),
      builder: (cameraState, previewSize, previewRect) {
        return _buildCameraStateWidget(cameraState);
      },
      onMediaCaptureEvent: _onMediaCaptureEvent,
    );
  }

  Widget _buildCameraStateWidget(CameraState cameraState) {
    return cameraState.when(
      onPreparingCamera: (state) =>
          const Center(child: CircularProgressIndicator(color: Colors.white)),
      onPhotoMode: (state) => GestureDetector(
        onTapUp: (details) =>
            _onTapToFocus(details, previewSize, previewRect, state),
        onScaleStart: (details) => _onScaleStart(details),
        onScaleUpdate: (details) => _onScaleUpdate(details, state),
        child: Container(), // 透明容器用于手势检测
      ),
      onVideoMode: (state) => GestureDetector(
        onTapUp: (details) =>
            _onTapToFocus(details, previewSize, previewRect, state),
        onScaleStart: (details) => _onScaleStart(details),
        onScaleUpdate: (details) => _onScaleUpdate(details, state),
        child: Container(), // 透明容器用于手势检测
      ),
      onVideoRecordingMode: (state) => Container(), // 录制时不处理手势
    );
  }

  void _onMediaCaptureEvent(MediaCaptureEvent event) {
    switch ((event.status, event.isPicture, event.isVideo)) {
      case (MediaCaptureStatus.capturing, true, false):
        setState(() {
          _isTakingPicture = true;
        });
        break;
      case (MediaCaptureStatus.success, true, false):
        setState(() {
          _isTakingPicture = false;
        });
        event.captureRequest.when(
          single: (single) {
            if (single.file != null) {
              _processPhoto(single.file!.path);
            }
          },
          multiple: (multiple) {
            // 处理多摄像头拍照
          },
        );
        break;
      case (MediaCaptureStatus.failure, true, false):
        setState(() {
          _isTakingPicture = false;
        });
        _showErrorDialog('拍照失败: ${event.exception}');
        break;
      default:
        break;
    }
  }

  Future<void> _processPhoto(String imagePath) async {
    try {
      // 如果有水印，应用水印
      if (_watermarkManager.hasWatermark) {
        final watermarkedPath = await _watermarkManager.applyWatermarkToImage(
          imagePath,
        );
        if (watermarkedPath != null) {
          // 删除原始文件
          await File(imagePath).delete();
          // 重命名水印文件
          await File(watermarkedPath).rename(imagePath);
        }
      }

      // 显示成功提示
      _showSuccessMessage('照片已保存');
    } catch (e) {
      _showErrorDialog('处理照片失败: $e');
    }
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('错误'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 2),
        backgroundColor: Colors.green,
      ),
    );
  }

  // 手势处理
  double _baseZoom = 1.0;

  void _onScaleStart(ScaleStartDetails details) {
    _baseZoom = _currentZoom;
  }

  void _onScaleUpdate(ScaleUpdateDetails details, dynamic state) {
    final newZoom = (_baseZoom * details.scale).clamp(_minZoom, _maxZoom);
    if (newZoom != _currentZoom) {
      setState(() {
        _currentZoom = newZoom;
      });
      // 更新相机缩放
      if (state is PhotoCameraState) {
        state.sensorConfig.setZoom(newZoom);
      } else if (state is VideoCameraState) {
        state.sensorConfig.setZoom(newZoom);
      }
    }
  }

  void _onTapToFocus(
    TapUpDetails details,
    PreviewSize previewSize,
    Rect previewRect,
    dynamic state,
  ) {
    final localPosition = details.localPosition;
    final dx = localPosition.dx / previewRect.width;
    final dy = localPosition.dy / previewRect.height;

    setState(() {
      _focusPoint = Offset(dx, dy);
      _showFocusRing = true;
    });

    // 执行聚焦动画
    _focusAnimationController.reset();
    _focusAnimationController.forward();

    // 2秒后隐藏聚焦环
    Timer(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          _showFocusRing = false;
        });
      }
    });
  }

  Widget _buildFocusRing() {
    return Positioned(
      left: _focusPoint.dx * MediaQuery.of(context).size.width - 40,
      top: _focusPoint.dy * MediaQuery.of(context).size.height - 40,
      child: AnimatedBuilder(
        animation: _focusAnimation,
        builder: (context, child) {
          return Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              border: Border.all(
                color: Colors.white.withOpacity(1.0 - _focusAnimation.value),
                width: 2,
              ),
              borderRadius: BorderRadius.circular(40),
            ),
          );
        },
      ),
    );
  }

  Widget _buildTopStatusBar() {
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: Container(
        padding: EdgeInsets.only(
          left: 16,
          right: 16,
          top: MediaQuery.of(context).padding.top + 8,
          bottom: 8,
        ),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Colors.black.withOpacity(0.7), Colors.transparent],
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // 闪光灯状态
            Icon(
              _isFlashOn ? Icons.flash_on : Icons.flash_off,
              color: Colors.white,
              size: 24,
            ),
            // 缩放显示
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.5),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                '${_currentZoom.toStringAsFixed(1)}x',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomControls() {
    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: Container(
        padding: EdgeInsets.only(
          left: 20,
          right: 20,
          top: 20,
          bottom: MediaQuery.of(context).padding.bottom + 20,
        ),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.bottomCenter,
            end: Alignment.topCenter,
            colors: [Colors.black.withOpacity(0.8), Colors.transparent],
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            // 相册按钮
            _buildControlButton(
              icon: Icons.photo_library,
              onPressed: _openGallery,
            ),

            // 拍照按钮
            _buildCaptureButton(),

            // 闪光灯按钮
            _buildControlButton(
              icon: _isFlashOn ? Icons.flash_on : Icons.flash_off,
              onPressed: _toggleFlash,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required VoidCallback onPressed,
  }) {
    return Container(
      width: 50,
      height: 50,
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.5),
        borderRadius: BorderRadius.circular(25),
        border: Border.all(color: Colors.white.withOpacity(0.3)),
      ),
      child: IconButton(
        icon: Icon(icon, color: Colors.white, size: 24),
        onPressed: onPressed,
      ),
    );
  }

  Widget _buildCaptureButton() {
    return GestureDetector(
      onTap: _isTakingPicture ? null : _takePicture,
      child: Container(
        width: 70,
        height: 70,
        decoration: BoxDecoration(
          color: _isTakingPicture ? Colors.grey : Colors.white,
          borderRadius: BorderRadius.circular(35),
          border: Border.all(color: Colors.white, width: 3),
        ),
        child: _isTakingPicture
            ? const Center(
                child: SizedBox(
                  width: 30,
                  height: 30,
                  child: CircularProgressIndicator(
                    strokeWidth: 3,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.black),
                  ),
                ),
              )
            : const Icon(Icons.camera_alt, color: Colors.black, size: 30),
      ),
    );
  }

  Widget _buildRightMenuButton() {
    return Positioned(
      top: MediaQuery.of(context).padding.top + 60,
      right: 20,
      child: Container(
        width: 50,
        height: 50,
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.5),
          borderRadius: BorderRadius.circular(25),
          border: Border.all(color: Colors.white.withOpacity(0.3)),
        ),
        child: IconButton(
          icon: const Icon(Icons.layers, color: Colors.white, size: 24),
          onPressed: _openPngMenu,
        ),
      ),
    );
  }

  Widget _buildLeftLensButton() {
    return Positioned(
      top: MediaQuery.of(context).padding.top + 60,
      left: 20,
      child: Container(
        width: 50,
        height: 50,
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.5),
          borderRadius: BorderRadius.circular(25),
          border: Border.all(color: Colors.white.withOpacity(0.3)),
        ),
        child: IconButton(
          icon: Icon(
            _isFrontCamera ? Icons.camera_front : Icons.camera_rear,
            color: Colors.white,
            size: 24,
          ),
          onPressed: _switchCamera,
        ),
      ),
    );
  }

  // 相机控制方法
  void _takePicture() async {
    if (_isTakingPicture) return;

    // 这里需要通过CameraAwesome的状态来拍照
    // 由于我们在builder中处理状态，需要通过全局状态管理
    // 暂时使用简单的状态标记
    setState(() {
      _isTakingPicture = true;
    });

    // 实际的拍照逻辑会在CameraAwesome的onMediaCaptureEvent中处理
    // 这里只是触发拍照动作
  }

  void _toggleFlash() {
    setState(() {
      _isFlashOn = !_isFlashOn;
    });
  }

  void _switchCamera() {
    setState(() {
      _isFrontCamera = !_isFrontCamera;
    });
  }

  void _openGallery() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const GalleryPage()),
    );
  }

  void _openPngMenu() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PngMenuPage(watermarkManager: _watermarkManager),
      ),
    );
  }
}
